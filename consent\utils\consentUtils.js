/**
 * Consent Utility Functions
 *
 * This file contains utility functions for consent management.
 * These functions handle the interaction with ABDM APIs for consent operations.
 *
 * Official ABDM Documentation:
 * https://kiranma72.github.io/abdm-docs/5-building-a-phr-app/managing-consents/index.html
 */

const axios = require('axios');
const Transaction = require('../../models/transaction.model');
const { sendConsentRequestNotification, sendConsentRequestRefreshNotification } = require('../../utils/websocketUtils');
const { ENDPOINTS, ABDM_CM_ID, ABDM_HIU_ID } = require('../../config/config');
const { getSessionToken, getHeader, generateTransactionId, makeApiCall, makeGetApiCall } = require('../../utils/abhaUtils');
const userTokenUtils = require('../../token/userTokenUtils'); 

/**
 * List consent requests for a user
 * Response structure {
  "size": 10,
  "limit": 10,
  "offset": 0,
  "requests": [
    {
      "requestId": "e5ec415f-c098-40f6-a0db-faa162fc5295",
      "purpose": {
        "text": "Care Management",
        "code": "CAREMGT",
        "refUri": "www.abc.com"
      },
      "patient": {
        "id": "abdulkalam@abdm"
      },
      "hip": {
        "id": "cowin_hip_01",
        "name": "Cowin",
        "type": "HIP"
      },
      "hiu": {
        "id": "cowin_hiu_01",
        "name": "Cowin",
        "type": "HIU"
      },
      "careContexts": [
        {
          "patientReference": "batman@tmh",
          "careContextReference": "Episode1"
        }
      ],
      "requester": {
        "name": "abdulkalam@abdm",
        "identifier": {
          "value": "REG1",
          "type": "MH1001",
          "system": "https://www.sample.com"
        }
      },
      "status": "GRANTED",
      "createdAt": "2021-09-28T12:30:08.573Z",
      "lastUpdated": "2021-09-28T12:30:08.573Z",
      "hiType": [
        "Prescription, DiagnosticReport, OPConsultation, DischargeSummary, ImmunizationRecord, HealthDocumentRecord, WellnessRecord,Invoice"
      ],
      "permission": {
        "accessMode": "VIEW",
        "dateRange": {
          "from": "2021-09-28T12:30:08.573Z",
          "to": "2021-09-28T12:30:08.573Z"
        },
        "dataEraseAt": "2021-09-28T12:30:08.573Z",
        "frequency": {
          "unit": "HOUR",
          "value": 1,
          "repeats": 0
        }
      }
    }
  ]
}

/**
 * List consent requests for a user and create/update consent records in the database
 * @param {String} userId - User ID
 * @param {Number} limit - Maximum number of results to return (optional)
 * @param {Number} offset - Offset for pagination (optional)
 * @param {String} status - Status filter (optional)
 * @returns {Promise<Object>} List of consent requests
 */
const listConsentRequests = async (userId, limit = 100, offset = 0, status = "ALL") => {
  try {
    console.log(`Listing consent requests for user ${userId}`);

    // Get session token and user token
    const sessionToken = await getSessionToken();
    const userToken = await userTokenUtils.getUserSessionToken(userId);;

    const requestId = generateTransactionId();

    const headers = {
      ...getHeader(sessionToken, requestId),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': userToken
    };



    const response = await makeGetApiCall(
      `${ENDPOINTS.CONSENT_LIST}?limit=${limit}&offset=${offset}&status=${status.toUpperCase()}`,
      sessionToken, { headers });

    if (!response.data || !response.data.consents) {
      console.log('No consents found in response');
      return { consents: [] };
    }

    return response.data;
  } catch (error) {
    console.error('Error listing consent requests:', error);
    throw new Error('Failed to list consent requests: ' + (error.message || 'Unknown error'));
  }
};


/**
 * Get consent request details
 * @param {String} userId - User ID
 * @param {String} consentRequestId - Consent request ID
 * @returns {Promise<Object>} Consent request details
 *
 * Response Body:
 * Code : 200 OK
 * {
 *   "requestId": "e5ec415f-c098-40f6-a0db-faa162fc5295",
 *   "purpose": {
 *     "text": "Care Management",
 *     "code": "CAREMGT",
 *     "refUri": "www.abc.com"
 *   },
 *   "patient": {
 *     "id": "abdulkalam@abdm"
 *   },
 *   "hip": {
 *     "id": "cowin_hip_01",
 *     "name": "Cowin",
 *     "type": "HIP"
 *   },
 *   "hiu": {
 *     "id": "cowin_hiu_01",
 *     "name": "Cowin",
 *     "type": "HIU"
 *   },
 *   "careContexts": [
 *     {
 *       "patientReference": "batman@tmh",
 *       "careContextReference": "Episode1"
 *     }
 *   ],
 *   "requester": {
 *     "name": "abdulkalam@abdm",
 *     "identifier": {
 *       "value": "REG1",
 *       "type": "MH1001",
 *       "system": "https://www.sample.com"
 *     }
 *   },
 *   "status": "GRANTED",
 *   "createdAt": "2021-09-28T12:30:08.573Z",
 *   "lastUpdated": "2021-09-28T12:30:08.573Z",
 *   "hiType": [
 *     "Prescription, DiagnosticReport, OPConsultation, DischargeSummary, ImmunizationRecord, HealthDocumentRecord, WellnessRecord,Invoice"
 *   ],
 *   "permission": {
 *     "accessMode": "VIEW",
 *     "dateRange": {
 *       "from": "2021-09-28T12:30:08.573Z",
 *       "to": "2021-09-28T12:30:08.573Z"
 *     },
 *     "dataEraseAt": "2021-09-28T12:30:08.573Z",
 *     "frequency": {
 *       "unit": "HOUR",
 *       "value": 1,
 *       "repeats": 0
 *     }
 *   }
 * }
 */
const getConsentRequestDetails = async (userId, consentRequestId) => {
  try {
    const sessionToken = await getSessionToken();
    const userToken = await userTokenUtils.getUserSessionToken(userId);

    if (!userToken) {
      throw new Error('User token not found');
    }

    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': userToken
    };

    // Make API call
    const response = await makeGetApiCall(
      ENDPOINTS.CONSENT_REQUEST_DETAIL.replace("{{consentRequestId}}", consentRequestId),
      sessionToken,
      { headers }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching consent detail:', error);
    throw new Error('Failed to fetch consent details: ' + (error.message || 'Unknown error'));
  }
};

//This API will be invoked by HIU to get the All-consent artifact details by request id.
/**
 * Get all consent artifact details by request ID
 * @param {String} userId - User ID
 * @param {String} consentRequestId - Consent request ID
 * @returns {Promise<Object>} Consent artifact details
 *
 * Response Body:
 * Code : 200 OK
 * [
 *   {
 *     "status": "GRANTED",
 *     "consentDetail": {
 *       "consentId": "e5ec415f-c098-40f6-a0db-faa162fc5295",
 *       "purpose": {
 *         "text": "Care Management",
 *         "code": "CAREMGT",
 *         "refUri": "www.abc.com"
 *       },
 *       "patient": {
 *         "id": "abdulkalam@abdm"
 *       },
 *       "hip": {
 *         "id": "cowin_hip_01",
 *         "name": "Cowin",
 *         "type": "HIP"
 *       },
 *       "hiu": {
 *         "id": "cowin_hiu_01",
 *         "name": "Cowin",
 *         "type": "HIU"
 *       },
 *       "careContexts": [
 *         {
 *           "patientReference": "batman@tmh",
 *           "careContextReference": "Episode1"
 *         }
 *       ],
 *       "requester": {
 *         "name": "abdulkalam@abdm",
 *         "identifier": {
 *           "value": "REG1",
 *           "type": "MH1001",
 *           "system": "https://www.sample.com"
 *         }
 *       },
 *       "createdAt": "2021-09-28T12:30:08.573Z",
 *       "lastUpdated": "2021-09-28T12:30:08.573Z",
 *       "schemaVersion": "v3",
 *       "consentManager": {
 *         "id": "abdm"
 *       },
 *       "hiTypes": [
 *         "Prescription"
 *       ],
 *       "permission": {
 *         "accessMode": "VIEW",
 *         "dateRange": {
 *           "from": "2021-09-28T12:30:08.573Z",
 *           "to": "2021-09-28T12:30:08.573Z"
 *         },
 *         "dataEraseAt": "2021-09-28T12:30:08.573Z",
 *         "frequency": {
 *           "unit": "HOUR",
 *           "value": 1,
 *           "repeats": 0
 *         }
 *       }
 *     },
 *     "signature": "Signature of CM as defined in W3C standards; Base64 encoded"
 *   }
 * ]
 */
const getAllConsentArtifactDetailsByRequestId = async (userId, consentRequestId) => {
  try {
    const sessionToken = await getSessionToken();
    const userToken = await userTokenUtils.getUserSessionToken(userId);

    if (!userToken) {
      throw new Error('User token not found');
    }

    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': userToken
    };

    // Make API call
    const response = await makeGetApiCall(
      ENDPOINTS.CONSENT_ARTEFACTS_DETAIL_BY_REQUEST_ID.replace("{{consentRequestId}}", consentRequestId),
      sessionToken,
      { headers }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching consent artefact details:', error);
    throw new Error('Failed to fetch consent artefact details: ' + (error.message || 'Unknown error'));
  }
};

/**
 * This API will be invoked by HIU to get the consent artifact details by artifact id.
 * @param {String} userId - User ID
 * @param {String} consentId - Consent ID
 * @returns {Promise<Object>} Consent artifact details
 *
 * Response Body:
 * Code: 200 OK
 * [
 *   {
 *     "status": "GRANTED",
 *     "consentDetail": {
 *       "consentId": "e5ec415f-c098-40f6-a0db-faa162fc5295",
 *       "purpose": {
 *         "text": "Care Management",
 *         "code": "CAREMGT",
 *         "refUri": "www.abc.com"
 *       },
 *       "patient": {
 *         "id": "abdulkalam@abdm"
 *       },
 *       "hip": {
 *         "id": "cowin_hip_01",
 *         "name": "Cowin",
 *         "type": "HIP"
 *       },
 *       "hiu": {
 *         "id": "cowin_hiu_01",
 *         "name": "Cowin",
 *         "type": "HIU"
 *       },
 *       "careContexts": [
 *         {
 *           "patientReference": "batman@tmh",
 *           "careContextReference": "Episode1"
 *         }
 *       ],
 *       "requester": {
 *         "name": "abdulkalam@abdm",
 *         "identifier": {
 *           "value": "REG1",
 *           "type": "MH1001",
 *           "system": "https://www.sample.com"
 *         }
 *       },
 *       "createdAt": "2021-09-28T12:30:08.573Z",
 *       "lastUpdated": "2021-09-28T12:30:08.573Z",
 *       "schemaVersion": "v3",
 *       "consentManager": {
 *         "id": "abdm"
 *       },
 *       "hiTypes": [
 *         "Prescription"
 *       ],
 *       "permission": {
 *         "accessMode": "VIEW",
 *         "dateRange": {
 *           "from": "2021-09-28T12:30:08.573Z",
 *           "to": "2021-09-28T12:30:08.573Z"
 *         },
 *         "dataEraseAt": "2021-09-28T12:30:08.573Z",
 *         "frequency": {
 *           "unit": "HOUR",
 *           "value": 1,
 *           "repeats": 0
 *         }
 *       }
 *     },
 *     "signature": "Signature of CM as defined in W3C standards; Base64 encoded"
 *   }
 * ]
 */
const getAllConsentArtifactDetailsByArtefactId = async (userId, consentId) => {
  try {
    const sessionToken = await getSessionToken();
    const userToken = await userTokenUtils.getUserSessionToken(userId);

    if (!userToken) {
      throw new Error('User token not found');
    }

    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': userToken
    };

    // Make API call
    const response = await makeGetApiCall(
      ENDPOINTS.CONSENT_ARTEFACTS_DETAIL_BY_ARTEFACT_ID.replace("{{consentId}}", consentId),
      sessionToken,
      { headers }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching consent artefact details:', error);
    throw new Error('Failed to fetch consent artefact details: ' + (error.message || 'Unknown error'));
  }
};

/**
 * This API will be invoked by HIU to get the All-consent artifact id for an ABHA Address.
 * @param {String} userId - User ID
 * @param {Number} limit - Maximum number of results to return
 * @param {Number} offset - Offset for pagination
 * @param {String} status - Status filter
 * @returns {Promise<Object>} Consent artifact details
 *
 * Response Body:
 * Code : 200 OK
 *
 * {
 *   "size": 10,
 *   "limit": 10,
 *   "offset": 0,
 *   "consentArtefacts": [
 *     {
 *       "status": "GRANTED",
 *       "consentDetail": {
 *         "consentId": "e5ec415f-c098-40f6-a0db-faa162fc5295",
 *         "purpose": {
 *           "text": "Care Management",
 *           "code": "CAREMGT",
 *           "refUri": "www.abc.com"
 *         },
 *         "patient": {
 *           "id": "abdulkalam@abdm"
 *         },
 *         "hip": {
 *           "id": "cowin_hip_01",
 *           "name": "Cowin",
 *           "type": "HIP"
 *         },
 *         "hiu": {
 *           "id": "cowin_hiu_01",
 *           "name": "Cowin",
 *           "type": "HIU"
 *         },
 *         "careContexts": [
 *           {
 *             "patientReference": "batman@tmh",
 *             "careContextReference": "Episode1"
 *           }
 *         ],
 *         "requester": {
 *           "name": "abdulkalam@abdm",
 *           "identifier": {
 *             "value": "REG1",
 *             "type": "MH1001",
 *             "system": "https://www.sample.com"
 *           }
 *         },
 *         "createdAt": "2021-09-28T12:30:08.573Z",
 *         "lastUpdated": "2021-09-28T12:30:08.573Z",
 *         "schemaVersion": "v3",
 *         "consentManager": {
 *           "id": "abdm"
 *         },
 *         "hiTypes": [
 *           "Prescription"
 *         ],
 *         "permission": {
 *           "accessMode": "VIEW",
 *           "dateRange": {
 *             "from": "2021-09-28T12:30:08.573Z",
 *             "to": "2021-09-28T12:30:08.573Z"
 *           },
 *           "dataEraseAt": "2021-09-28T12:30:08.573Z",
 *           "frequency": {
 *             "unit": "HOUR",
 *             "value": 1,
 *             "repeats": 0
 *           }
 *         }
 *       },
 *       "signature": "..."
 *     }
 *   ]
 * }
 */
const getAllConsentArtifactDetailsByAbhaAddress = async (userId, limit, offset, status) => {
  try {
    const sessionToken = await getSessionToken();
    const userToken = await userTokenUtils.getUserSessionToken(userId);

    if (!userToken) {
      throw new Error('User token not found');
    }

    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': userToken
    };

    // Make API call
    const response = await makeGetApiCall(
      `${ENDPOINTS.ARTEFACT_LIST_BY_ABHA_ADDRESS}?limit=${limit}&offset=${offset}&status=${status}`,
      sessionToken,
      { headers }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching consent artefact details:', error);
    throw new Error('Failed to fetch consent artefact details: ' + (error.message || 'Unknown error'));
  }
};
/**
 * Set up auto-approve consent for a user
 * @param {String} userId - User ID
 * @returns {Promise<Object>} Auto-approve setup result
 */
const consentAutoApprove = async (userId) => {
  try {
    // Get session token and user token
    const sessionToken = await getSessionToken();
    const userToken = await userTokenUtils.getUserSessionToken(userId);

    if (!userToken) {
      throw new Error('User token not found');
    }

    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': userToken
    };

    const requestBody = {
      isApplicableForAllHIPs: true,
      hiu: {
        id: ABDM_HIU_ID
      },
      includedSources: [
        {
          hiTypes: [
            "Prescription",
            "DiagnosticReport",
            "OPConsultation",
            "DischargeSummary",
            "ImmunizationRecord",
            "HealthDocumentRecord",
            "WellnessRecord",
            "Invoice"
          ],
          purpose: {
            text: "Care Management",
            code: "CAREMGT",
            refUri: "https://www.abdm.gov.in"
          },
          period: {
            from: new Date().toISOString(),
            to: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString() // 1 year from now
          }
        }
      ],
      excludedSources: []
    };

    const response = await makeApiCall(
      ENDPOINTS.AUTO_APPROVE_CONSENT,
      requestBody,
      sessionToken,
      { headers }
    );

    console.log('Auto-approve consent response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error setting up auto-approve consent:', error);
    throw new Error('Failed to set up auto-approve consent: ' + (error.message || 'Unknown error'));
  }
}

/**
 * Toggle auto-approve consent for a specific consent ID
 * @param {String} consentId - Consent ID
 * @param {Boolean} enable - Whether to enable or disable auto-approve
 * @param {String} userId - User ID
 * @returns {Promise<Object>} Toggle result
 */
const toggleConsentAutoApprove = async (consentId, enable, userId) => {
  try {
    // Get session token and user token
    const sessionToken = await getSessionToken();
    const userToken = await userTokenUtils.getUserSessionToken(userId);

    if (!userToken) {
      throw new Error('User token not found');
    }

    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': userToken
    };

    const endpoint = enable
      ? ENDPOINTS.ENABLE_CONSENT_AUTO_APPROVE.replace('{{consentId}}', consentId)
      : ENDPOINTS.DISABLE_CONSENT_AUTO_APPROVE.replace('{{consentId}}', consentId);

    const response = await makeApiCall(
      endpoint,
      {},
      sessionToken,
      { headers }
    );

    console.log(`Auto-approve consent ${enable ? 'enabled' : 'disabled'} response:`, response.data);
    return response.data;
  } catch (error) {
    console.error(`Error ${enable ? 'enabling' : 'disabling'} auto-approve consent:`, error);
    throw new Error(`Failed to ${enable ? 'enable' : 'disable'} auto-approve consent: ` + (error.message || 'Unknown error'));
  }
}

/**
 * Fetch consent artifact details from HIE-CM with callback
 * This function initiates an asynchronous fetch of consent details using a callback URL.
 * It creates a transaction record to track the request and the callback will be handled separately.
 * The actual consent details will be received in the callback and processed by processConsentFetchCallback.
 * Response Body: Code : 202 OK
 * @param {String} consentId - Consent ID
 * @returns {Promise<Object>} Initial response with requestId and timestamp
 */
const fetchConsentDetails = async (userId, consentId) => {
  try {
    const sessionToken = await getSessionToken();
    const requestId = generateTransactionId();
    const timestamp = new Date().toISOString();

    // Prepare request body
    const requestBody = {
      consentId: consentId,
    };

    // Prepare headers
    const headers = {
      ...getHeader(sessionToken, requestId),
      'X-CM-ID': ABDM_CM_ID,
      'X-HIU-ID': ABDM_HIU_ID,
    };

    // Make API call
    const response = await makeApiCall(
      ENDPOINTS.CONSENT_FETCH,
      requestBody,
      sessionToken,
      { headers }
    );

    // Create a transaction record to track this consent fetch request
    const transaction = new Transaction({
      transactionId: requestId,
      userId,
      type: 'CONSENT_FETCH',
      status: 'INITIATED',
      requestData: {
        consentId,
        requestBody
      },
      responseData: response.data
    });

    await transaction.save();
    console.log(`Created transaction record for consent fetch: ${consentId}`);

    return {
      ...response.data,
      requestId,
      timestamp
    };
  } catch (error) {
    console.error('Error fetching consent details with callback:', error);
    throw new Error('Failed to fetch consent details: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Get consent request status
 * @param {String} consentRequestId - Consent request ID
 * @param {String} consentRequestId - Request ID from which the request was made
 * @returns {Promise<Object>} Consent request status
 */
const getConsentRequestStatus = async (consentRequestId, transactionId) => {
  try {
    const sessionToken = await getSessionToken();
    const requestId = generateTransactionId();
    const headers = {
      ...getHeader(sessionToken, requestId),
      'X-CM-ID': ABDM_CM_ID,
      'X-HIU-ID': ABDM_HIU_ID
    }

    // Prepare request body
    const requestBody = {
      requestId: requestId,
      timestamp: new Date().toISOString(),
      consentRequestId: consentRequestId
    };
    const transaction = await Transaction.findOne({
      type: 'CONSENT_REQUEST',
      transactionId: transactionId
    });

    transaction.requestData = {
      ...transaction.requestData,
      consentRequestId: consentRequestId
    }
    await transaction.save();

    if (!transaction) {
      console.error(`No transaction record found for consent request ID: ${transactionId}`);
      return;
    }
    // Create a transaction record to track this consent request
    await new Transaction({
      userId: transaction.userId,
      type: transaction.type,
      abhaAddress: transaction.abhaAddress,
      transactionId: requestId,
      type: 'CONSENT_REQUEST_STATUS',
      status: 'INITIATED',
      requestData: {
        consentRequestId: consentRequestId
      }
    }).save();


    console.log(`Updating transaction record for consent request: ${transactionId} with consentRequestId: ${consentRequestId}`)


    // Make API call
    const response = await axios.post(
      ENDPOINTS.CONSENT_REQUEST_STATUS,
      requestBody,
      {
        headers
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error getting consent request status:', error);
    throw new Error('Failed to get consent request status: ' + (error.message || 'Unknown error'));
  }
};


/**
 * Handle consent request on-status callback
 * Request Body : {
 *   "consentRequest": {
 *     "id": "a7554d1c-3cc7-42be-823b-766038ea73b7",
 *     "status": "REQUESTED"
 *   },
 *   "error": null,
 *   "response": {
 *     "requestId": "2bf5fff7-f2a8-4014-9558-59723e1e7d16"
 *   },
 *   "resp": null
 * }
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @returns {Object} Response with acknowledgement
 */
const handleConsentRequestOnStatusCallback = async (consentRequest, error, transactionId) => {
  try {
    if (error) {
      console.error('Error in consent request status callback:', error);
      return;
    }

    if (!consentRequest || !consentRequest.id) {
      console.error('Invalid consent request status callback format:', consentRequest);
      return;
    }
    // Find transaction record for this consent request
    const transaction = await Transaction.findOne({
      type: 'CONSENT_REQUEST_STATUS',
      transactionId: transactionId
    });

    if (!transaction) {
      console.error(`No transaction record found for consent request ID: ${consentRequest.id}`);
      return;
    }

    // notify frontend to refresh consent list
    sendConsentRequestRefreshNotification(transaction.userId, {
      status: consentRequest.status
    });

    await transaction.deleteOne();
    console.log(`Consent request status callback processed for consent request ID: ${consentRequest.id} with status: ${consentRequest.status}`);
  } catch (error) {
    console.error('Error in consent request status callback:', error);
    throw new Error('Failed to process consent request status callback: ' + (error.message || 'Unknown error'));
  }
};


/**
 * Handle consent request notification
 * @param {Object} notification - Notification object
 * @returns {Promise<void>}
 *
 * Notification structure:
 * {
 *  "notification": {
 *    "consentRequestId": "e3c74829-3f82-4f94-959e-e10f57bcd57b",
 *    "status": "GRANTED",
 *    "reason": null,
 *    "consentArtefacts": [
 *      {
 *        "id": "<consent-artefact-id>"
 *      }
 *    ]
 *  }
 * }
 */
const handleConsentRequestNotification = async (status, consentRequestId, consentArtefacts, reason) => {
  // Always respond with 202 to acknowledge receipt
  res.status(202).send();
  // Process notification asynchronously
  const transaction = await Transaction.findOne({
    type: 'CONSENT_REQUEST',
    'requestData.consentRequestId': consentRequestId
  }).lean();

  if (!transaction) {
    throw new Error(`No transaction found for consent request ID: ${consentRequestId}`);
  }

  // If consent artefacts are provided (for GRANTED), store them
  if (consentArtefacts?.length) {
    await Promise.all(consentArtefacts.map(async (artefact) => {
      if (!artefact.id) {
        throw new Error('Consent artefact missing ID');
      }
      try {
        await fetchConsentDetails(transaction.userId, artefact.id);
      } catch (error) {
        console.error(`Error fetching details for consent artefact ID: ${artefact.id}:`, error);
      }
    }));
    console.log(`Consent request notify callback processed for consent request ID: ${consentRequestId} with status: ${status}`);

  } else {
    console.log("No consent artefacts provided");
  }
};



/**
 * Create a consent PIN
 * @param {String} userId - User ID
 * @param {String} abhaAddress - ABHA address
 * @param {String} pin - PIN to set
 * @returns {Promise<Object>} PIN creation result
 */
const createConsentPin = async (userId, pin) => {
  try {
    // Get user session token
    let userToken;
    try {
      userToken = await userTokenUtils.getUserSessionToken(userId);
    } catch (error) {
      console.error(`Error getting user token: ${error.message}`);
      throw new Error(`Failed to get user token: ${error.message}`);
    }


    if (!userToken) {
      throw new Error('User token not found');
    }

    const sessionToken = await getSessionToken();
    // Call ABDM API to create PIN
    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-Auth-Token': userToken
    };

    // Prepare request body according to ABDM documentation
    const requestBody = {
      pin: pin
    };

    // Make API call to ABDM
    await makeApiCall(
      ENDPOINTS.CONSENT_CREATE_PIN,
      requestBody,
      sessionToken,
      { headers }
    );

    // Temporarily commenting out ConsentPin model usage
    console.log(`PIN created for user ${userId}`);

    return { success: true, message: 'Consent PIN created successfully' };
  } catch (error) {
    console.error('Error creating consent PIN:', error);
    throw new Error('Failed to create consent PIN: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Verify a consent PIN
 * @param {String} userId - User ID
 * @param {String} pin - PIN to verify
 * @param {String} requestId - Request ID
 * @param {String} scope - Scope of the verification
 * @returns {Promise<Object>} Verification result
 */
const verifyConsentPin = async (userId, pin, requestId, scope) => {
  try {
    // Get user session token
    let userToken;
    try {
      userToken = await userTokenUtils.getUserSessionToken(userId);
    } catch (error) {
      console.error(`Error getting user token: ${error.message}`);
      throw new Error(`Failed to get user token: ${error.message}`);
    }

    if (!userToken) {
      throw new Error('User token not found');
    }

    const sessionToken = await getSessionToken();
    // Call ABDM API to verify PIN
    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-Auth-Token': userToken
    };

    // Prepare request body according to ABDM documentation
    const requestBody = {
      pin: pin,
      requestId: requestId,
      scope: scope
    };

    // Make API call to ABDM
    const response = await makeApiCall(
      ENDPOINTS.CONSENT_VERIFY_PIN,
      requestBody,
      sessionToken,
      { headers }
    );

    // Temporarily commenting out ConsentPin model usage
    console.log(`PIN verified for user ${userId}`);

    return {
      success: true,
      message: 'PIN verified successfully',
      temporaryToken: response.data.temporaryToken
    };
  } catch (error) {
    console.error('Error verifying consent PIN:', error);
    throw new Error('Failed to verify consent PIN: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Change a consent PIN
 * @param {String} userId - User ID
 * @param {String} oldPin - Old PIN
 * @param {String} newPin - New PIN
 * @returns {Promise<Object>} PIN change result
 */
const changeConsentPin = async (userId, oldPin, newPin) => {
  try {
    // First verify the old PIN with ABDM
    const requestId = generateTransactionId();
    const scope = 'profile.changepin';

    const verifyResult = await verifyConsentPin(
      userId,
      oldPin,
      requestId,
      scope
    );

    if (!verifyResult.success) {
      return verifyResult;
    }

    // Use the temporary token from verification to change the PIN
    const temporaryToken = verifyResult.temporaryToken;


    const sessionToken = await getSessionToken();
    // Call ABDM API to create PIN
    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-Auth-Token': temporaryToken
    };

    // Prepare request body according to ABDM documentation
    const requestBody = {
      pin: newPin
    };

    // Make API call to ABDM
    await axios.post(
      ENDPOINTS.CONSENT_CHANGE_PIN,
      requestBody,
      { headers }
    );

    // Temporarily commenting out ConsentPin model usage
    console.log(`PIN changed for user ${userId}`);

    return { success: true, message: 'Consent PIN changed successfully' };
  } catch (error) {
    console.error('Error changing consent PIN:', error);
    throw new Error('Failed to change consent PIN: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Generate OTP for forgot PIN
 * @param {String} userId - User ID
 * @returns {Promise<Object>} OTP generation result
 */
const generateForgotPinOtp = async (userId) => {
  try {
    // Get user session token
    let userToken;
    try {
      userToken = await userTokenUtils.getUserSessionToken(userId);
    } catch (error) {
      console.error(`Error getting user token: ${error.message}`);
      throw new Error(`Failed to get user token: ${error.message}`);
    }

    if (!userToken) {
      throw new Error('User token not found');
    }

    // Temporarily commenting out ConsentPin model usage
    console.log(`Generating OTP for user ${userId}`);

    // Call ABDM API to generate OTP
    const sessionToken = await getSessionToken();
    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-Auth-Token': userToken
    };

    // Make API call with empty body as per ABDM documentation
    const response = await axios.post(
      ENDPOINTS.CONSENT_FORGOT_PIN_GENERATE_OTP,
      {},
      { headers }
    );

    return {
      success: true,
      message: 'OTP generated successfully',
      sessionId: response.data.sessionId
    };
  } catch (error) {
    console.error('Error generating forgot PIN OTP:', error);
    throw new Error('Failed to generate forgot PIN OTP: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Validate OTP for forgot PIN
 * @param {String} userId - User ID
 * @param {String} sessionId - Session ID
 * @param {String} otp - OTP
 * @returns {Promise<Object>} OTP validation result
 */
const validateForgotPinOtp = async (userId, sessionId, otp) => {
  try {
    // Get user session token
    let X_TOKEN;
    try {
      X_TOKEN = await userTokenUtils.getUserSessionToken(userId);
    } catch (error) {
      console.error(`Error getting user token: ${error.message}`);
      throw new Error(`Failed to get user token: ${error.message}`);
    }

    if (!X_TOKEN) {
      throw new Error('User token not found');
    }

    // Temporarily commenting out ConsentPin model usage
    console.log(`Validating OTP for user ${userId}`);

    // Call ABDM API to validate OTP
    const sessionToken = await getSessionToken();
    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': X_TOKEN
    };

    // Prepare request body according to ABDM documentation
    const requestBody = {
      sessionId: sessionId,
      value: otp
    };

    // Make API call
    const response = await axios.post(
      ENDPOINTS.CONSENT_FORGOT_PIN_VALIDATE_OTP,
      requestBody,
      { headers }
    );

    return {
      success: true,
      message: 'OTP validated successfully',
      temporaryToken: response.data.temporaryToken
    };
  } catch (error) {
    console.error('Error validating forgot PIN OTP:', error);
    throw new Error('Failed to validate forgot PIN OTP: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Reset consent PIN
 * @param {String} userId - User ID
 * @param {String} temporaryToken - Temporary token from OTP validation
 * @param {String} newPin - New PIN
 * @returns {Promise<Object>} PIN reset result
 */
const resetConsentPin = async (userId, temporaryToken, newPin) => {
  try {
    // Call ABDM API to reset PIN
    const sessionToken = await getSessionToken();
    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': temporaryToken
    };

    // Prepare request body according to ABDM documentation
    const requestBody = {
      pin: newPin
    };

    // Make API call - note that ABDM uses PUT method for reset-pin
    await axios.put(
      ENDPOINTS.CONSENT_RESET_PIN,
      requestBody,
      { headers }
    );

    // Temporarily commenting out ConsentPin model usage
    console.log(`PIN reset for user ${userId}`);

    return { success: true, message: 'Consent PIN reset successfully' };
  } catch (error) {
    console.error('Error resetting consent PIN:', error);
    throw new Error('Failed to reset consent PIN: ' + (error.message || 'Unknown error'));
  }
};


const acknowledgeConsentNotification = async (consentArtefacts, consentRequestId, error = null) => {
  try {
    // "error": {
    //   "code": "ABDM-1001",
    //   "message": "unable to connect database"
    //   },
    const sessionToken = await getSessionToken();
    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
    };

    // Prepare request body
    const requestBody = {
      acknowledgement: consentArtefacts.map(artefact => ({
        status: "OK",
        consentId: artefact.id
      })),
      error: error,
      response: {
        requestId: consentRequestId
      }
    };

    // Make API call
    const response = await makeApiCall(
      ENDPOINTS.CONSENT_NOTIFICATION_ACKNOWLEDGED,
      requestBody,
      sessionToken,
      { headers }
    );

    return response.data;
  } catch (error) {
    console.error('Error acknowledging consent notification:', error);
    throw new Error('Failed to acknowledge consent notification: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Initialize a consent request
 * Response Body: 202 Accepted
 * @param {String} patientId - ABHA address of the patient
 * @param {Object} consentDetail - Consent details
 * @param {String} userId - User ID initiating the request
 * @returns {Promise<Object>} Consent request initialization result
 */
const initializeConsentRequest = async (patientId, consentDetail, userId) => {
  let transaction;
  try {
    const requestId = generateTransactionId();
    const sessionToken = await getSessionToken();
    const headers = {
      ...getHeader(sessionToken, requestId),
      'X-CM-ID': ABDM_CM_ID,
      'X-HIU-ID': ABDM_HIU_ID
    };

    // Prepare request body
    const requestBody = {
      requestId: requestId,
      timestamp: new Date().toISOString(),
      consent: {
        purpose: {
          text: consentDetail.purpose,
          code: consentDetail.purposeCode || "CAREMGT",
          refUri: "www.abdm.gov.in"
        },
        patient: {
          id: patientId
        },
        hiu: {
          id: ABDM_HIU_ID,
          name: ENDPOINTS.ABDM_NAME
        },
        hip: null,
        careContexts: null,
        requester: {
          name: consentDetail.requesterName || ENDPOINTS.ABDM_NAME,
          identifier: {
            type: "REGNO",
            value: consentDetail.requesterIdentifier || ABDM_HIU_ID,
            system: "https://abdm.gov.in/systems"
          }
        },
        hiTypes: consentDetail.hiTypes || ["DiagnosticReport", "Prescription", "DischargeSummary"],
        permission: {
          accessMode: consentDetail.accessMode || "VIEW",
          dateRange: {
            from: consentDetail.dateRange?.from || new Date().toISOString(),
            to: consentDetail.dateRange?.to || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
          },
          dataEraseAt: consentDetail.dataEraseAt || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year from now
          frequency: {
            unit: consentDetail.frequency?.unit || "HOUR",
            value: consentDetail.frequency?.value || 1,
            repeats: consentDetail.frequency?.repeats || 0
          }
        },
        callBackUrl: ENDPOINTS.CALLBACK_CONSENT_REQUEST_ON_INIT
      }
    };

    // Create a transaction record to track this consent request
    transaction = new Transaction({
      userId,
      transactionId: requestId,
      type: 'CONSENT_REQUEST',
      status: 'INITIATED',
      abhaAddress: patientId,
      requestData: requestBody
    });
    await transaction.save();

    // We'll create the consent record after the callback is received
    // No need to create it here as we don't have a consentId yet

    // Make API call
    const response = await makeApiCall(
      ENDPOINTS.CONSENT_REQUEST_INIT,
      requestBody,
      sessionToken,
      {
        headers
      }

    );

    console.log(`Created transaction record for consent request: ${requestId}`);
    return response.data;
  } catch (error) {
    if (transaction) {
      await transaction.deleteOne();
    }
    console.error('Error initializing consent request:', error);
    throw new Error('Failed to initialize consent request: ' + (error.message || 'Unknown error'));
  }
};

 


/**
 * Process consent fetch callback
 * This function processes the callback data received after fetching consent details.
 * It updates the transaction record and creates or updates the consent record.
 * Request Body:
 * {
 *   "consent": {
 *     "status": "GRANTED",
 *     "consentDetail": {
 *       "consentId": "<consent-id>",
 *       "hip": {
 *         "id": "<hip-id>"
 *       },
 *       "hiu": {
 *         "id": "<hiu-id>"
 *       },
 *       "hiTypes": [
 *         "Prescription",
 *         "DiagnosticReport",
 *         "DischargeSummary"
 *       ],
 *       "patient": {
 *         "id": "<abha-address>"
 *       },
 *       "purpose": {
 *         "text": "Care Management",
 *         "code": "CAREMGT",
 *         "refUri": "www.abdm.gov.in"
 *       },
 *       "createdAt": "<timestamp>",
 *       "requester": {
 *         "name": "<requester-name>",
 *         "identifier": {
 *           "value": "<value>",
 *           "type": "REGNO",
 *           "system": "<system-url>"
 *         }
 *       },
 *       "permission": {
 *         "accessMode": "VIEW",
 *         "dateRange": {
 *           "from": "<from-date>",
 *           "to": "<to-date>"
 *         },
 *         "dataEraseAt": "<erase-date>",
 *         "frequency": {
 *           "unit": "DAY",
 *           "value": 1,
 *           "repeats": 0
 *         }
 *       },
 *       "careContexts": [
 *         {
 *           "patientReference": "<patient-reference>",
 *           "careContextReference": "<care-context-reference>"
 *         }
 *       ],
 *       "consentManager": {
 *         "id": "<cm-id>"
 *       }
 *     },
 *     "signature": "<signature>"
 *   },
 *   "error": null,
 *   "response": {
 *     "requestId": "<request-id>"
 *   },
 *   "resp": null
 * }
 * @param {Object} callbackData - Callback data from ABDM
 * @returns {Promise<Object>} Result of the callback processing
 */
const processConsentOnFetchCallback = async (callbackData) => {
  try {
    console.log('Processing consent fetch callback');

    // Extract consent details
    const { consent, error, response } = callbackData;
    if (!consent) {
      console.warn('No consent details found in callback');
      return { processed: false, reason: 'No consent details found' };
    }

    // Extract consent details from the structure
    const { status, consentDetail } = consent;


    const consentId = consentDetail.consentId;

    console.log(`Processing consent fetch callback for consent ID: ${consentId}, status: ${status}`);

    // Find transaction record for this consent fetch
    // First try to find by requestData.consentId
    const transaction = await Transaction.findOne({
      transactionId: response.requestId,
      type: 'CONSENT_FETCH'
    });

    // If not found, try to find by response.requestId in CONSENT_REQUEST transactions
    if (!transaction) {
      console.warn('transaction not found');
      return { processed: false, reason: 'Transaction not found' };
    }

    // Send WebSocket notification if requested
    sendConsentRequestNotification(transaction.userId, {
      consent: consentDetail,
      status: status
    })
 


    return {
      processed: true,
      consentId,
      status,
      error: error
    };
  } catch (error) {
    console.error('Error processing consent fetch callback:', error);
    throw new Error('Failed to process consent fetch callback: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Approve a consent request
 * This function calls the ABDM API to approve a consent request with the specified parameters.
 *
 * @param {String} userId - User ID of the patient approving the consent
 * @param {String} requestId - The consent request ID to approve
 * @param {Array} consents - Array of consent objects with hiTypes, hip, careContexts, and permission details
 * @returns {Promise<Object>} Approval result
 */
const approveConsentRequest = async (userId, consents, consentRequestId) => {
  try {
    console.log(`Approving consent request: ${consentRequestId}`);

    // Get session token and user token
    const sessionToken = await getSessionToken();
    const userToken = await userTokenUtils.getUserSessionToken(userId);;

    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': userToken
    };

    // Prepare request body
    const requestBody = {
      consents: consents
    };

    // Make API call

    const response = await makeApiCall(
      ENDPOINTS.CONSENT_APPROVE.replace('{{consentRequestId}}', consentRequestId),
      requestBody,
      sessionToken,
      { headers }
    );


    return response.data;
  } catch (error) {
    console.error('Error approving consent request:', error);
    throw new Error('Failed to approve consent request: ' + (error.message || 'Unknown error'));
  }
};

/**
 * Deny a consent request
 * This function calls the ABDM API to deny a consent request with the specified reason.
 *
 * @param {String} userId - User ID of the patient denying the consent
 * @param {String} consentRequestId - The consent request ID to deny
 * @param {String} reason - Reason for denying the consent request
 * @returns {Promise<Object>} Denial result
 */
const denyConsentRequest = async (userId, consentRequestId, reason) => {
  try {
    console.log(`Denying consent request: ${consentRequestId} with reason: ${reason}`);
    // Get session token and user token
    const sessionToken = await getSessionToken();
    const userToken = await userTokenUtils.getUserSessionToken(userId);;

    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': userToken
    };

    // Make API call
    const response = await makeApiCall(
      ENDPOINTS.CONSENT_DENY.replace('{{consentRequestId}}', consentRequestId),
      { reason: reason },
      sessionToken,
      { headers }
    );


    return response.data;
  } catch (error) {
    console.error('Error denying consent request:', error);
    throw new Error('Failed to deny consent request: ' + (error.message || 'Unknown error'));
  }
};


/**
 * Revoke a consent
 * @param {String} consentId - Consent ID
 * @param {String} reason - Reason for revocation
 * @param {String} userId - User ID (optional, for transaction tracking)
 * @returns {Promise<Object>} Revocation result
 */
const revokeConsent = async (consentId, userId) => {
  try {
    // Get session token and user token
    const sessionToken = await getSessionToken();
    const userToken = await userTokenUtils.getUserSessionToken(userId);;


    const headers = {
      ...getHeader(sessionToken),
      'X-CM-ID': ABDM_CM_ID,
      'X-AUTH-TOKEN': userToken
    };


    // Make API call
    console.log(`Making API call to ${ENDPOINTS.CONSENT_REVOKE}`);
    const response = await makeApiCall(
      ENDPOINTS.CONSENT_REVOKE,
      { consents: [consentId] },
      sessionToken,
      { headers }
    );

    return response.data;
  } catch (error) {

    console.error('Error revoking consent:', error);
    throw new Error('Failed to revoke consent: ' + (error.message || 'Unknown error'));
  }
};


module.exports = {
  getConsentRequestStatus,
  handleConsentRequestOnStatusCallback,
  handleConsentRequestNotification,
  createConsentPin,
  verifyConsentPin,
  changeConsentPin,
  generateForgotPinOtp,
  validateForgotPinOtp,
  resetConsentPin,
  acknowledgeConsentNotification,
  initializeConsentRequest,
  fetchConsentDetails,
  processConsentOnFetchCallback, 
  listConsentRequests,
  revokeConsent,
  approveConsentRequest,
  denyConsentRequest,
  consentAutoApprove,
  toggleConsentAutoApprove,

  // Additional consent detail retrieval functions
  getConsentRequestDetails,
  getAllConsentArtifactDetailsByRequestId,
  getAllConsentArtifactDetailsByArtefactId,
  getAllConsentArtifactDetailsByAbhaAddress
};
